# Graphiti MCP Server - 生产就绪容器化部署

[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![Neo4j](https://img.shields.io/badge/Neo4j-5.26.0-green.svg)](https://neo4j.com/)
[![Gemini](https://img.shields.io/badge/Google_Gemini-Supported-orange.svg)](https://ai.google.dev/)

基于 Graphiti 知识图谱的 MCP (Model Context Protocol) 服务器的完整生产环境容器化部署方案。支持 Google Gemini、OpenAI 和 Azure OpenAI 作为 LLM 提供商。

## 🚀 快速启动

### 前置要求

- Docker 和 Docker Compose
- 至少 4GB 可用内存
- Google Gemini API 密钥（或其他 LLM 提供商的 API 密钥）

### 一键部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd graphiti-mcp-server

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入你的 API 密钥

# 3. 启动服务
docker-compose up -d

# 4. 验证部署
./scripts/verify_deployment.sh --wait
```

### 服务访问地址

- **MCP 服务器**: http://localhost:8089 (SSE 传输)
- **Neo4j Browser**: http://localhost:7474 (用户名: neo4j, 密码: demodemo)
- **Neo4j Bolt**: bolt://localhost:7687

## 📋 项目架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │───▶│ Graphiti MCP     │───▶│   Google        │
│   (Claude/etc)  │    │ Server (8089)    │    │   Gemini API    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Neo4j Graph    │
                       │   Database       │
                       │   (7474/7687)    │
                       └──────────────────┘
```

### 核心组件

1. **Graphiti MCP Server**: 提供 MCP 协议接口，处理知识图谱操作
2. **Neo4j Database**: 图数据库，存储知识图谱数据
3. **Google Gemini**: LLM 提供商，用于实体提取和推理
4. **Docker Network**: 自定义网络确保服务间通信

## ⚙️ 配置说明

### 环境变量配置

主要配置文件为 `.env`，包含以下关键配置：

#### LLM 提供商配置

```bash
# 选择 LLM 提供商: 'gemini', 'openai', 'azure_openai'
LLM_PROVIDER=gemini

# Google Gemini 配置
GOOGLE_API_KEY=your_api_key_here
GEMINI_BASE_URL=http://bt.itzxc.cn:8000  # 可选，用于代理
MODEL_NAME=gemini-2.0-flash-001
SMALL_MODEL_NAME=gemini-2.0-flash-001
```

#### 数据库配置

```bash
# Neo4j 配置
NEO4J_URI=bolt://neo4j:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=demodemo
```

#### 服务器配置

```bash
# MCP 服务器配置
MCP_SERVER_HOST=0.0.0.0
LLM_TEMPERATURE=0.0
SEMAPHORE_LIMIT=10
```

### Docker Compose 配置

服务配置包括：

- **网络**: `graphiti-network` 自定义桥接网络
- **端口映射**: 8089:8000 (MCP), 7474:7474 (Neo4j HTTP), 7687:7687 (Neo4j Bolt)
- **健康检查**: 自动监控服务状态
- **重启策略**: `unless-stopped` 确保服务可靠性
- **数据持久化**: Neo4j 数据和日志卷

## 🔧 MCP 工具功能

Graphiti MCP 服务器提供以下工具：

### 内存管理工具

1. **add_memory**: 添加信息到知识图谱
   ```json
   {
     "name": "会议记录",
     "episode_body": "今天讨论了新产品发布计划...",
     "source": "text",
     "group_id": "project_alpha"
   }
   ```

2. **search_memory_nodes**: 搜索实体节点
   ```json
   {
     "query": "产品发布相关的实体",
     "max_nodes": 10,
     "group_ids": ["project_alpha"]
   }
   ```

3. **search_memory_facts**: 搜索关系事实
   ```json
   {
     "query": "用户偏好相关的关系",
     "max_facts": 10
   }
   ```

### 数据管理工具

4. **get_episodes**: 获取最近的记忆片段
5. **get_entity_edge**: 获取特定实体关系
6. **delete_episode**: 删除记忆片段
7. **delete_entity_edge**: 删除实体关系
8. **clear_graph**: 清空整个知识图谱

### 状态监控

9. **get_status**: 获取服务器和数据库连接状态

## 🧪 测试和验证

### 自动化验证

运行完整的部署验证：

```bash
# 等待服务启动并验证
./scripts/verify_deployment.sh --wait

# 或使用 Python 版本（需要安装依赖）
python scripts/verify_deployment.py --wait
```

验证内容包括：
- ✅ 环境变量完整性
- ✅ Neo4j HTTP 和 Bolt 连接
- ✅ MCP 服务器健康状态
- ✅ Gemini API 连接性
- ✅ Docker 服务状态

### 手动测试

1. **Neo4j 连接测试**:
   ```bash
   curl http://localhost:7474
   ```

2. **MCP 服务器健康检查**:
   ```bash
   curl http://localhost:8089/health
   ```

3. **端到端功能测试**:
   ```bash
   # 使用 MCP 客户端连接到 sse://localhost:8089
   # 调用 add_memory 工具添加测试数据
   # 调用 search_memory_nodes 验证数据检索
   ```

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败

**症状**: `docker-compose up` 失败或服务无法启动

**解决方案**:
```bash
# 检查日志
docker-compose logs graphiti-mcp-server
docker-compose logs neo4j

# 检查端口占用
netstat -tulpn | grep -E ':(7474|7687|8089)'

# 重新构建镜像
docker-compose build --no-cache
```

#### 2. Neo4j 连接问题

**症状**: MCP 服务器报告数据库连接失败

**解决方案**:
```bash
# 检查 Neo4j 状态
docker-compose exec neo4j cypher-shell -u neo4j -p demodemo "RETURN 1"

# 检查网络连接
docker-compose exec graphiti-mcp-server ping neo4j

# 验证环境变量
docker-compose exec graphiti-mcp-server env | grep NEO4J
```

#### 3. Gemini API 连接问题

**症状**: LLM 调用失败或超时

**解决方案**:
```bash
# 验证 API 密钥
echo $GOOGLE_API_KEY

# 测试自定义端点
curl -I $GEMINI_BASE_URL

# 检查网络连接
docker-compose exec graphiti-mcp-server curl -I $GEMINI_BASE_URL
```

#### 4. 内存不足

**症状**: 服务频繁重启或性能问题

**解决方案**:
```bash
# 调整 Neo4j 内存设置（在 docker-compose.yml 中）
NEO4J_dbms_memory_heap_max__size=2G
NEO4J_dbms_memory_pagecache_size=1G

# 减少并发限制
SEMAPHORE_LIMIT=5
```

### 日志分析

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f graphiti-mcp-server
docker-compose logs -f neo4j

# 查看最近的错误
docker-compose logs --tail=50 graphiti-mcp-server | grep -i error
```

## 📚 高级配置

### 自定义 LLM 提供商

支持切换到不同的 LLM 提供商：

#### OpenAI 配置
```bash
LLM_PROVIDER=openai
OPENAI_API_KEY=your_openai_key
MODEL_NAME=gpt-4.1-mini
SMALL_MODEL_NAME=gpt-4.1-nano
```

#### Azure OpenAI 配置
```bash
LLM_PROVIDER=azure_openai
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment
OPENAI_API_KEY=your_azure_key
```

### 性能调优

1. **并发控制**: 调整 `SEMAPHORE_LIMIT` 根据 API 限制
2. **内存优化**: 根据数据量调整 Neo4j 内存设置
3. **网络优化**: 使用本地 DNS 解析加速服务发现

### 安全配置

1. **API 密钥管理**: 使用 Docker secrets 或外部密钥管理
2. **网络隔离**: 配置防火墙规则限制外部访问
3. **数据加密**: 启用 Neo4j 传输加密

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目基于 Apache 2.0 许可证开源。

## 🆘 支持

如遇问题，请：
1. 查看故障排除部分
2. 运行验证脚本诊断问题
3. 提交 Issue 并附上日志信息
