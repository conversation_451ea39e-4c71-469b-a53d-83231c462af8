# Graphiti MCP Server Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# LLM Provider Configuration
# =============================================================================

# LLM Provider: 'openai', 'azure_openai', or 'gemini'
LLM_PROVIDER=gemini

# =============================================================================
# Google Gemini Configuration (when LLM_PROVIDER=gemini)
# =============================================================================

# Google API Key for Gemini
GOOGLE_API_KEY=your_google_api_key_here

# Custom Gemini Base URL (optional, for proxy or custom endpoints)
GEMINI_BASE_URL=http://bt.itzxc.cn:8000

# Gemini Model Names
MODEL_NAME=gemini-2.0-flash-001
SMALL_MODEL_NAME=gemini-2.0-flash-001

# =============================================================================
# OpenAI Configuration (when LLM_PROVIDER=openai)
# =============================================================================

# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# OpenAI Model Names (uncomment if using OpenAI)
# MODEL_NAME=gpt-4.1-mini
# SMALL_MODEL_NAME=gpt-4.1-nano

# =============================================================================
# Azure OpenAI Configuration (when LLM_PROVIDER=azure_openai)
# =============================================================================

# Azure OpenAI Endpoint
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/

# Azure OpenAI API Version
# AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure OpenAI Deployment Name
# AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment-name

# Use Managed Identity for Azure (true/false)
# AZURE_OPENAI_USE_MANAGED_IDENTITY=false

# =============================================================================
# Embedder Configuration
# =============================================================================

# Embedder Model Name
EMBEDDER_MODEL_NAME=text-embedding-3-small

# Azure OpenAI Embedding Configuration (if using Azure for embeddings)
# AZURE_OPENAI_EMBEDDING_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_EMBEDDING_API_VERSION=2024-02-15-preview
# AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME=your-embedding-deployment
# AZURE_OPENAI_EMBEDDING_API_KEY=your_azure_embedding_api_key

# =============================================================================
# Neo4j Database Configuration
# =============================================================================

# Neo4j Connection URI
NEO4J_URI=bolt://neo4j:7687

# Neo4j Authentication
NEO4J_USER=neo4j
NEO4J_PASSWORD=demodemo

# =============================================================================
# MCP Server Configuration
# =============================================================================

# MCP Server Host (for SSE transport)
MCP_SERVER_HOST=0.0.0.0

# LLM Temperature (0.0-2.0, lower values are more deterministic)
LLM_TEMPERATURE=0.0

# Concurrency limit for Graphiti operations
# Decrease if experiencing rate limit errors, increase for higher throughput
SEMAPHORE_LIMIT=10

# =============================================================================
# Docker Compose Configuration
# =============================================================================

# Neo4j Port (default: 7687)
NEO4J_PORT=7687
