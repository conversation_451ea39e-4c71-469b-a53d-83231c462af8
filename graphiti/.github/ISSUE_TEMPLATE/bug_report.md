---
name: Bug Report
about: Create a report to help us improve Graphiti
title: '[BUG] '
labels: bug
assignees: ''
---

## Bug Description
A clear and concise description of what the bug is.

## Steps to Reproduce
Provide a minimal code example that reproduces the issue:

```python
# Your code here
```

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Environment
- **Graphiti Version**: [e.g. 0.15.1]
- **Python Version**: [e.g. 3.11.5]
- **Operating System**: [e.g. macOS 14.0, Ubuntu 22.04]
- **Database Backend**: [e.g. Neo4j 5.26, FalkorDB 1.1.2]
- **LLM Provider & Model**: [e.g. OpenAI gpt-4.1, Anthropic claude-4-sonnet, Google gemini-2.5-flash]

## Installation Method
- [ ] pip install
- [ ] uv add
- [ ] Development installation (git clone)

## Error Messages/Traceback
```
Paste the full error message and traceback here
```

## Configuration
```python
# Relevant configuration or initialization code
```

## Additional Context
- Does this happen consistently or intermittently?
- Which component are you using? (core library, REST server, MCP server)
- Any recent changes to your environment?
- Related issues or similar problems you've encountered?

## Possible Solution
If you have ideas about what might be causing the issue or how to fix it, please share them here.