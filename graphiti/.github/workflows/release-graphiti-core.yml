name: Release to PyPI

on:
  push:
    tags: ["v*.*.*"]

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
    environment:
      name: release
      url: https://pypi.org/p/zep-cloud
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          version: "latest"
      - name: Compare pyproject version with tag
        run: |
          TAG_VERSION=${GITHUB_REF#refs/tags/}
          PROJECT_VERSION=$(uv run python -c "import tomllib; print('v' + tomllib.load(open('pyproject.toml', 'rb'))['project']['version'])")
          if [ "$TAG_VERSION" != "$PROJECT_VERSION" ]; then
            echo "Tag version $TAG_VERSION does not match the project version $PROJECT_VERSION"
            exit 1
          fi
      - name: Build project for distribution
        run: uv build
      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
