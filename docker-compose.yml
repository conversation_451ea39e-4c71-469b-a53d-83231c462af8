networks:
  graphiti-network:
    driver: bridge

services:
  neo4j:
    image: neo4j:5.26.0
    container_name: graphiti-neo4j
    networks:
      - graphiti-network
    ports:
      - "7474:7474"  # Neo4j Browser Web interface
      - "7687:7687"  # Bolt protocol database connection
    environment:
      - NEO4J_AUTH=${NEO4J_USER:-neo4j}/${NEO4J_PASSWORD:-demodemo}
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=1G
      - NEO4J_dbms_memory_pagecache_size=512m
      - NEO4J_server_memory_heap_initial__size=512m
      - NEO4J_server_memory_heap_max__size=1G
      - NEO4J_server_memory_pagecache_size=512m
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    healthcheck:
      test: ["CMD", "wget", "-O", "/dev/null", "http://localhost:7474"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped

  graphiti-mcp-server:
    build:
      context: ./graphiti/mcp_server
      dockerfile: Dockerfile
    container_name: graphiti-mcp-server
    networks:
      - graphiti-network
    ports:
      - "8089:8000"  # External port 8089 maps to container internal port 8000
    env_file:
      - path: .env
        required: false
    environment:
      - NEO4J_URI=${NEO4J_URI:-bolt://neo4j:7687}
      - NEO4J_USER=${NEO4J_USER:-neo4j}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-demodemo}
      - LLM_PROVIDER=${LLM_PROVIDER:-gemini}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GEMINI_BASE_URL=${GEMINI_BASE_URL}
      - MODEL_NAME=${MODEL_NAME:-gemini-2.0-flash-001}
      - SMALL_MODEL_NAME=${SMALL_MODEL_NAME:-gemini-2.0-flash-001}
      - EMBEDDER_MODEL_NAME=${EMBEDDER_MODEL_NAME:-text-embedding-3-small}
      - LLM_TEMPERATURE=${LLM_TEMPERATURE:-0.0}
      - SEMAPHORE_LIMIT=${SEMAPHORE_LIMIT:-10}
      - MCP_SERVER_HOST=${MCP_SERVER_HOST:-0.0.0.0}
      - PATH=/root/.local/bin:${PATH}
    depends_on:
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    command: ["uv", "run", "graphiti_mcp_server.py", "--transport", "sse", "--host", "0.0.0.0"]

volumes:
  neo4j_data:
  neo4j_logs:
