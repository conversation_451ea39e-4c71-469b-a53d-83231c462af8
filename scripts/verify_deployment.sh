#!/bin/bash

# Graphiti MCP Server Deployment Verification Script
# This script verifies that all components are working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
NEO4J_HOST=${NEO4J_HOST:-localhost}
NEO4J_HTTP_PORT=${NEO4J_HTTP_PORT:-7474}
NEO4J_BOLT_PORT=${NEO4J_BOLT_PORT:-7687}
MCP_SERVER_HOST=${MCP_SERVER_HOST:-localhost}
MCP_SERVER_PORT=${MCP_SERVER_PORT:-8089}
MAX_WAIT_TIME=${MAX_WAIT_TIME:-120}

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Functions
print_header() {
    echo -e "\n${BOLD}${BLUE}============================================================${NC}"
    echo -e "${BOLD}${BLUE}$(printf '%*s' $(((60-${#1})/2)) '')$1$(printf '%*s' $(((60-${#1})/2)) '')${NC}"
    echo -e "${BOLD}${BLUE}============================================================${NC}\n"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    ((PASSED_CHECKS++))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    ((FAILED_CHECKS++))
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ $1${NC}"
}

check_command() {
    if command -v $1 &> /dev/null; then
        print_success "$1 is available"
        return 0
    else
        print_error "$1 is not available"
        return 1
    fi
}

check_environment() {
    print_header "Environment Variables Check"
    ((TOTAL_CHECKS++))
    
    local all_good=true
    
    # Required variables
    local required_vars=("LLM_PROVIDER" "NEO4J_URI" "NEO4J_USER" "NEO4J_PASSWORD")
    
    for var in "${required_vars[@]}"; do
        if [[ -n "${!var}" ]]; then
            print_success "$var is set"
        else
            print_error "$var is not set (REQUIRED)"
            all_good=false
        fi
    done
    
    # Optional variables
    print_info "\nOptional environment variables:"
    local optional_vars=("GOOGLE_API_KEY" "GEMINI_BASE_URL" "MODEL_NAME" "SMALL_MODEL_NAME")
    
    for var in "${optional_vars[@]}"; do
        if [[ -n "${!var}" ]]; then
            print_success "$var is set"
        else
            print_warning "$var is not set (optional)"
        fi
    done
    
    if $all_good; then
        print_success "Environment variables check passed"
        return 0
    else
        print_error "Environment variables check failed"
        return 1
    fi
}

check_neo4j_http() {
    print_header "Neo4j HTTP Interface Check"
    ((TOTAL_CHECKS++))
    
    local url="http://${NEO4J_HOST}:${NEO4J_HTTP_PORT}"
    
    if curl -s -f "$url" > /dev/null; then
        print_success "Neo4j HTTP interface is accessible at $url"
        return 0
    else
        print_error "Neo4j HTTP interface is not accessible at $url"
        return 1
    fi
}

check_neo4j_bolt() {
    print_header "Neo4j Bolt Connection Check"
    ((TOTAL_CHECKS++))
    
    if nc -z "$NEO4J_HOST" "$NEO4J_BOLT_PORT" 2>/dev/null; then
        print_success "Neo4j Bolt port $NEO4J_BOLT_PORT is accessible"
        return 0
    else
        print_error "Cannot connect to Neo4j Bolt port $NEO4J_BOLT_PORT"
        return 1
    fi
}

check_mcp_server() {
    print_header "MCP Server Health Check"
    ((TOTAL_CHECKS++))
    
    local url="http://${MCP_SERVER_HOST}:${MCP_SERVER_PORT}/health"
    
    if curl -s -f "$url" > /dev/null; then
        print_success "MCP server health endpoint is accessible at $url"
        
        # Try to get the actual health status
        local response=$(curl -s "$url" 2>/dev/null)
        if echo "$response" | grep -q '"status":"ok"'; then
            print_success "MCP server reports healthy status"
        else
            print_warning "MCP server responded but status unclear"
        fi
        return 0
    else
        print_error "MCP server health endpoint is not accessible at $url"
        return 1
    fi
}

check_docker_services() {
    print_header "Docker Services Check"
    ((TOTAL_CHECKS++))
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        return 1
    fi
    
    if ! docker compose ps &> /dev/null; then
        print_error "Docker Compose services are not running"
        return 1
    fi
    
    # Check individual services
    local services=("graphiti-neo4j" "graphiti-mcp-server")
    local all_running=true
    
    for service in "${services[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$service"; then
            local status=$(docker ps --filter "name=$service" --format "{{.Status}}")
            if [[ $status == *"Up"* ]]; then
                print_success "$service container is running"
            else
                print_error "$service container is not healthy: $status"
                all_running=false
            fi
        else
            print_error "$service container is not running"
            all_running=false
        fi
    done
    
    if $all_running; then
        print_success "All Docker services are running"
        return 0
    else
        print_error "Some Docker services are not running properly"
        return 1
    fi
}

wait_for_services() {
    print_header "Waiting for Services to Start"
    
    local start_time=$(date +%s)
    local timeout=$MAX_WAIT_TIME
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $timeout ]]; then
            print_error "Services did not start within $timeout seconds"
            return 1
        fi
        
        print_info "Checking services... (${elapsed}s elapsed)"
        
        # Quick check if services are responding
        if curl -s -f "http://${NEO4J_HOST}:${NEO4J_HTTP_PORT}" > /dev/null && \
           curl -s -f "http://${MCP_SERVER_HOST}:${MCP_SERVER_PORT}/health" > /dev/null; then
            print_success "All services are ready!"
            return 0
        fi
        
        sleep 5
    done
}

run_basic_functionality_test() {
    print_header "Basic Functionality Test"
    ((TOTAL_CHECKS++))
    
    # Test MCP server status endpoint
    local status_url="http://${MCP_SERVER_HOST}:${MCP_SERVER_PORT}/status"
    
    if curl -s -f "$status_url" > /dev/null; then
        print_success "MCP server status endpoint is accessible"
        return 0
    else
        print_warning "MCP server status endpoint test failed (this may be normal for SSE transport)"
        return 0  # Don't fail the overall test for this
    fi
}

print_summary() {
    print_header "Verification Summary"
    
    echo -e "${BOLD}Results: ${PASSED_CHECKS}/${TOTAL_CHECKS} checks passed${NC}"
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        echo -e "\n${GREEN}${BOLD}🎉 All checks passed! Deployment is ready.${NC}"
        return 0
    else
        echo -e "\n${RED}${BOLD}❌ $FAILED_CHECKS checks failed. Please review the errors above.${NC}"
        return 1
    fi
}

# Main execution
main() {
    print_header "Graphiti MCP Server Deployment Verification"
    
    # Check if we should wait for services
    if [[ "$1" == "--wait" ]]; then
        wait_for_services || exit 1
    fi
    
    # Load environment variables if .env file exists
    if [[ -f .env ]]; then
        print_info "Loading environment variables from .env file"
        set -a
        source .env
        set +a
    fi
    
    # Run all checks
    check_environment
    check_docker_services
    check_neo4j_http
    check_neo4j_bolt
    check_mcp_server
    run_basic_functionality_test
    
    # Print summary and exit
    print_summary
}

# Check for required commands
print_info "Checking required commands..."
check_command "curl" || exit 1
check_command "nc" || print_warning "netcat (nc) not available, skipping some network checks"
check_command "docker" || print_warning "Docker not available, skipping Docker checks"

# Run main function
main "$@"
