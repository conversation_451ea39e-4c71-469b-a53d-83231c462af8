#!/bin/bash

# Graphiti MCP Server Quick Start Script
# This script provides an interactive setup and deployment process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

print_header() {
    echo -e "\n${BOLD}${BLUE}============================================================${NC}"
    echo -e "${BOLD}${BLUE}$(printf '%*s' $(((60-${#1})/2)) '')$1$(printf '%*s' $(((60-${#1})/2)) '')${NC}"
    echo -e "${BOLD}${BLUE}============================================================${NC}\n"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ $1${NC}"
}

check_prerequisites() {
    print_header "Checking Prerequisites"
    
    local all_good=true
    
    # Check Docker
    if command -v docker &> /dev/null; then
        print_success "Docker is installed"
        
        # Check if Docker is running
        if docker info &> /dev/null; then
            print_success "Docker daemon is running"
        else
            print_error "Docker daemon is not running. Please start Docker."
            all_good=false
        fi
    else
        print_error "Docker is not installed. Please install Docker first."
        all_good=false
    fi
    
    # Check Docker Compose
    if docker compose version &> /dev/null; then
        print_success "Docker Compose is available"
    else
        print_error "Docker Compose is not available. Please install Docker Compose."
        all_good=false
    fi
    
    # Check available memory
    local available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [[ $available_memory -gt 4000 ]]; then
        print_success "Sufficient memory available (${available_memory}MB)"
    else
        print_warning "Low available memory (${available_memory}MB). Recommended: 4GB+"
    fi
    
    if ! $all_good; then
        print_error "Prerequisites check failed. Please resolve the issues above."
        exit 1
    fi
}

setup_environment() {
    print_header "Environment Configuration"
    
    if [[ -f .env ]]; then
        print_info "Found existing .env file"
        read -p "Do you want to reconfigure? (y/N): " reconfigure
        if [[ ! $reconfigure =~ ^[Yy]$ ]]; then
            print_info "Using existing configuration"
            return 0
        fi
    fi
    
    print_info "Setting up environment configuration..."
    
    # Copy template if it doesn't exist
    if [[ ! -f .env ]]; then
        if [[ -f .env.example ]]; then
            cp .env.example .env
            print_success "Created .env from template"
        else
            print_error ".env.example not found. Please ensure you're in the correct directory."
            exit 1
        fi
    fi
    
    # Interactive configuration
    echo -e "\n${BOLD}LLM Provider Configuration${NC}"
    echo "1) Google Gemini (recommended)"
    echo "2) OpenAI"
    echo "3) Azure OpenAI"
    read -p "Choose LLM provider (1-3): " provider_choice
    
    case $provider_choice in
        1)
            sed -i 's/^LLM_PROVIDER=.*/LLM_PROVIDER=gemini/' .env
            echo -e "\n${BOLD}Google Gemini Configuration${NC}"
            read -p "Enter your Google API Key: " api_key
            sed -i "s/^GOOGLE_API_KEY=.*/GOOGLE_API_KEY=$api_key/" .env
            
            read -p "Enter custom Gemini base URL (optional, press Enter to skip): " base_url
            if [[ -n $base_url ]]; then
                sed -i "s|^GEMINI_BASE_URL=.*|GEMINI_BASE_URL=$base_url|" .env
            fi
            ;;
        2)
            sed -i 's/^LLM_PROVIDER=.*/LLM_PROVIDER=openai/' .env
            read -p "Enter your OpenAI API Key: " api_key
            sed -i "s/^OPENAI_API_KEY=.*/OPENAI_API_KEY=$api_key/" .env
            ;;
        3)
            sed -i 's/^LLM_PROVIDER=.*/LLM_PROVIDER=azure_openai/' .env
            read -p "Enter your Azure OpenAI endpoint: " endpoint
            read -p "Enter your Azure OpenAI API key: " api_key
            read -p "Enter your deployment name: " deployment
            # Add Azure configuration to .env
            echo "AZURE_OPENAI_ENDPOINT=$endpoint" >> .env
            echo "OPENAI_API_KEY=$api_key" >> .env
            echo "AZURE_OPENAI_DEPLOYMENT_NAME=$deployment" >> .env
            ;;
        *)
            print_error "Invalid choice. Using default Gemini configuration."
            ;;
    esac
    
    # Neo4j configuration
    echo -e "\n${BOLD}Database Configuration${NC}"
    read -p "Neo4j password (default: demodemo): " neo4j_password
    neo4j_password=${neo4j_password:-demodemo}
    sed -i "s/^NEO4J_PASSWORD=.*/NEO4J_PASSWORD=$neo4j_password/" .env
    
    print_success "Environment configuration completed"
}

build_and_start() {
    print_header "Building and Starting Services"
    
    print_info "Building Docker images..."
    if docker-compose build; then
        print_success "Docker images built successfully"
    else
        print_error "Failed to build Docker images"
        exit 1
    fi
    
    print_info "Starting services..."
    if docker-compose up -d; then
        print_success "Services started successfully"
    else
        print_error "Failed to start services"
        exit 1
    fi
    
    print_info "Waiting for services to be ready..."
    sleep 10
}

verify_deployment() {
    print_header "Verifying Deployment"
    
    if [[ -x "./scripts/verify_deployment.sh" ]]; then
        print_info "Running deployment verification..."
        if ./scripts/verify_deployment.sh --wait; then
            print_success "Deployment verification passed"
            return 0
        else
            print_error "Deployment verification failed"
            return 1
        fi
    else
        print_warning "Verification script not found, performing basic checks..."
        
        # Basic manual checks
        local all_good=true
        
        # Check if containers are running
        if docker-compose ps | grep -q "Up"; then
            print_success "Docker containers are running"
        else
            print_error "Some Docker containers are not running"
            all_good=false
        fi
        
        # Check Neo4j
        if curl -s -f http://localhost:7474 > /dev/null; then
            print_success "Neo4j is accessible"
        else
            print_error "Neo4j is not accessible"
            all_good=false
        fi
        
        # Check MCP server
        if curl -s -f http://localhost:8089/health > /dev/null; then
            print_success "MCP server is accessible"
        else
            print_error "MCP server is not accessible"
            all_good=false
        fi
        
        return $all_good
    fi
}

show_access_info() {
    print_header "Access Information"
    
    echo -e "${BOLD}Service URLs:${NC}"
    echo -e "  ${CYAN}MCP Server:${NC}     http://localhost:8089"
    echo -e "  ${CYAN}Neo4j Browser:${NC}  http://localhost:7474"
    echo -e "  ${CYAN}Neo4j Bolt:${NC}     bolt://localhost:7687"
    
    echo -e "\n${BOLD}Neo4j Credentials:${NC}"
    local neo4j_password=$(grep "^NEO4J_PASSWORD=" .env | cut -d'=' -f2)
    echo -e "  ${CYAN}Username:${NC} neo4j"
    echo -e "  ${CYAN}Password:${NC} $neo4j_password"
    
    echo -e "\n${BOLD}MCP Client Configuration:${NC}"
    echo -e "  ${CYAN}Transport:${NC} SSE"
    echo -e "  ${CYAN}URL:${NC}       http://localhost:8089"
    
    echo -e "\n${BOLD}Useful Commands:${NC}"
    echo -e "  ${CYAN}View logs:${NC}        docker-compose logs -f"
    echo -e "  ${CYAN}Stop services:${NC}    docker-compose down"
    echo -e "  ${CYAN}Restart services:${NC} docker-compose restart"
    echo -e "  ${CYAN}Verify deployment:${NC} ./scripts/verify_deployment.sh"
}

show_next_steps() {
    print_header "Next Steps"
    
    echo -e "${BOLD}1. Test the MCP Server${NC}"
    echo -e "   Connect your MCP client to: ${CYAN}http://localhost:8089${NC}"
    echo -e "   Use SSE transport protocol"
    
    echo -e "\n${BOLD}2. Explore Neo4j Database${NC}"
    echo -e "   Open: ${CYAN}http://localhost:7474${NC}"
    echo -e "   Login with credentials shown above"
    
    echo -e "\n${BOLD}3. Add Your First Memory${NC}"
    echo -e "   Use the 'add_memory' tool in your MCP client"
    echo -e "   Example: Add information about a project or conversation"
    
    echo -e "\n${BOLD}4. Search and Retrieve${NC}"
    echo -e "   Use 'search_memory_nodes' and 'search_memory_facts' tools"
    echo -e "   Explore the knowledge graph that gets built"
    
    echo -e "\n${BOLD}5. Monitor and Maintain${NC}"
    echo -e "   Check logs: ${CYAN}docker-compose logs -f${NC}"
    echo -e "   Monitor health: ${CYAN}curl http://localhost:8089/health${NC}"
}

cleanup_on_failure() {
    print_header "Cleaning Up After Failure"
    
    print_info "Stopping services..."
    docker-compose down
    
    print_info "Removing containers..."
    docker-compose rm -f
    
    print_warning "You may want to check the logs for more details:"
    echo "  docker-compose logs"
}

main() {
    print_header "Graphiti MCP Server Quick Start"
    
    # Trap to cleanup on failure
    trap cleanup_on_failure ERR
    
    # Run setup steps
    check_prerequisites
    setup_environment
    build_and_start
    
    if verify_deployment; then
        show_access_info
        show_next_steps
        
        echo -e "\n${GREEN}${BOLD}🎉 Deployment completed successfully!${NC}"
        echo -e "${GREEN}Your Graphiti MCP Server is ready to use.${NC}"
    else
        print_error "Deployment verification failed. Check the logs for details:"
        echo "  docker-compose logs"
        exit 1
    fi
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
